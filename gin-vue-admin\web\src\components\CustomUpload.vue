<template>
  <div class="custom-upload-wrapper">
    <!-- 图片/视频上传模式 -->
    <div v-if="isImageOrVideo" class="upload-container" :style="containerStyle">
      <el-upload
        ref="upload"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :on-error="handleError"
        :multiple="multiple"
        :accept="acceptTypes"
        :disabled="disabled"
        class="image-video-uploader"
      >
        <div class="upload-content">
          <!-- 已上传的文件预览 -->
          <div v-if="fileList.length > 0" class="file-preview-container">
            <div 
              v-for="(file, index) in fileList" 
              :key="index" 
              class="file-preview-item"
              @click.stop="previewFileHandler(file)"
            >
              <img v-if="isImage(file)" :src="file.url" class="preview-image" />
              <video v-else-if="isVideo(file)" :src="file.url" class="preview-video" controls />
              <div class="file-actions">
                <i class="el-icon-view" @click.stop="previewFileHandler(file)"></i>
                <i class="el-icon-delete" @click.stop="removeFile(index)"></i>
              </div>
            </div>
          </div>
          
          <!-- 上传按钮 -->
          <div v-if="!multiple || fileList.length === 0" class="upload-trigger">
            <i class="el-icon-plus upload-icon"></i>
            <div class="upload-text">点击上传</div>
          </div>
        </div>
      </el-upload>
    </div>

    <!-- 文档上传模式 -->
    <div v-else class="document-upload">
      <el-upload
        ref="upload"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :file-list="documentFileList"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-remove="handleRemove"
        :multiple="multiple"
        :accept="acceptTypes"
        :disabled="disabled"
        class="document-uploader"
      >
        <el-button size="small" type="primary" :disabled="disabled">
          <i class="el-icon-upload"></i> 选择文件
        </el-button>
        <div slot="tip" class="el-upload__tip">
          支持 {{ acceptTypes }} 格式文件
        </div>
      </el-upload>
    </div>

    <!-- 图片裁剪对话框 -->
    <el-dialog
      title="图片裁剪"
      :visible.sync="cropDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      @close="closeCropDialog"
    >
      <div class="crop-container">
        <vue-cropper
          ref="cropper"
          :img="cropImageSrc"
          :output-size="1"
          :output-type="cropOutputType"
          :info="true"
          :full="false"
          :can-move="true"
          :can-move-box="true"
          :original="false"
          :auto-crop="true"
          :auto-crop-width="cropWidth"
          :auto-crop-height="cropHeight"
          :fixed="cropFixed"
          :fixed-number="cropFixedNumber"
          :center-box="true"
          :info-true="true"
          :fixed-box="false"
        ></vue-cropper>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeCropDialog">取消</el-button>
        <el-button type="primary" @click="confirmCrop">确认裁剪</el-button>
      </div>
    </el-dialog>

    <!-- 文件预览对话框 -->
    <el-dialog
      title="文件预览"
      :visible.sync="previewDialogVisible"
      width="80%"
      center
    >
      <div class="preview-container">
        <!-- 图片预览 -->
        <img v-if="previewFile && isImage(previewFile)" :src="previewFile.url" class="preview-full-image" />
        
        <!-- 视频预览 -->
        <video v-else-if="previewFile && isVideo(previewFile)" :src="previewFile.url" controls class="preview-full-video" />
        
        <!-- PDF预览 -->
        <iframe 
          v-else-if="previewFile && isPdf(previewFile)" 
          :src="previewFile.url" 
          class="preview-pdf"
          frameborder="0"
        ></iframe>
        
        <!-- 其他文档 -->
        <div v-else class="preview-document">
          <i class="el-icon-document"></i>
          <p>{{ previewFile && previewFile.name }}</p>
          <el-button type="primary" @click="downloadFile(previewFile)">下载文件</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { VueCropper } from 'vue-cropper'
import { mapGetters } from 'vuex'
import axios from 'axios'

export default {
  name: 'CustomUpload',
  components: {
    VueCropper
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    // 当前值
    value: {
      type: [String, Array],
      default: () => []
    },
    // 上传类型：image, video, document, auto
    uploadType: {
      type: String,
      default: 'auto',
      validator: value => ['image', 'video', 'document', 'auto'].includes(value)
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 容器宽度
    width: {
      type: [String, Number],
      default: 180
    },
    // 容器高度
    height: {
      type: [String, Number],
      default: 180
    },
    // 是否启用裁剪
    enableCrop: {
      type: Boolean,
      default: false
    },
    // 裁剪宽度
    cropWidth: {
      type: Number,
      default: 200
    },
    // 裁剪高度
    cropHeight: {
      type: Number,
      default: 200
    },
    // 是否固定裁剪比例
    cropFixed: {
      type: Boolean,
      default: false
    },
    // 裁剪比例 [宽, 高]
    cropFixedNumber: {
      type: Array,
      default: () => [1, 1]
    },
    // 文件大小限制 (KB)
    fileSize: {
      type: Number,
      default: 2048
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 自定义接受的文件类型
    accept: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileList: [],
      documentFileList: [],
      cropDialogVisible: false,
      previewDialogVisible: false,
      cropImageSrc: '',
      cropOutputType: 'jpeg',
      currentCropFile: null,
      previewFile: null,
      
      // 文件类型定义
      imageTypes: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'],
      videoTypes: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'],
      documentTypes: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
    }
  },
  computed: {
    ...mapGetters('user', ['userInfo', 'token']),
    
    // 上传地址
    uploadUrl() {
      return `${process.env.VUE_APP_BASE_API}/fileUploadAndDownload/upload`
    },
    
    // 上传请求头
    uploadHeaders() {
      return {
        'x-token': this.token
      }
    },
    
    // 容器样式
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height: typeof this.height === 'number' ? `${this.height}px` : this.height
      }
    },
    
    // 是否为图片或视频上传模式
    isImageOrVideo() {
      return ['image', 'video', 'auto'].includes(this.uploadType)
    },
    
    // 接受的文件类型
    acceptTypes() {
      if (this.accept) return this.accept
      
      switch (this.uploadType) {
        case 'image':
          return '.jpg,.jpeg,.png,.gif,.webp,.bmp,.svg'
        case 'video':
          return '.mp4,.avi,.mov,.wmv,.flv,.webm,.mkv'
        case 'document':
          return '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt'
        default:
          return '.jpg,.jpeg,.png,.gif,.webp,.bmp,.svg,.mp4,.avi,.mov,.wmv,.flv,.webm,.mkv,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt'
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.initFileList(newVal)
      },
      immediate: true
    }
  },
  methods: {
    // 初始化文件列表
    initFileList(value) {
      if (!value) {
        this.fileList = []
        this.documentFileList = []
        return
      }
      
      const files = Array.isArray(value) ? value : [value]
      this.fileList = files.filter(file => file && file.url).map(file => ({
        name: file.name || this.getFileNameFromUrl(file.url),
        url: file.url,
        type: this.getFileType(file.url)
      }))
      
      this.documentFileList = this.fileList.map(file => ({
        name: file.name,
        url: file.url
      }))
    },
    
    // 从URL获取文件名
    getFileNameFromUrl(url) {
      return url.split('/').pop() || 'unknown'
    },
    
    // 获取文件类型
    getFileType(url) {
      const ext = url.split('.').pop()?.toLowerCase()
      if (this.imageTypes.includes(ext)) return 'image'
      if (this.videoTypes.includes(ext)) return 'video'
      if (this.documentTypes.includes(ext)) return 'document'
      return 'unknown'
    },
    
    // 判断是否为图片
    isImage(file) {
      return file.type === 'image' || this.imageTypes.includes(this.getFileExtension(file.url))
    },
    
    // 判断是否为视频
    isVideo(file) {
      return file.type === 'video' || this.videoTypes.includes(this.getFileExtension(file.url))
    },
    
    // 判断是否为PDF
    isPdf(file) {
      return this.getFileExtension(file.url) === 'pdf'
    },
    
    // 获取文件扩展名
    getFileExtension(url) {
      return url.split('.').pop()?.toLowerCase() || ''
    },

    // 上传前检查
    beforeUpload(file) {
      // 检查文件类型
      if (!this.checkFileType(file)) {
        this.$message.error('不支持的文件类型')
        return false
      }

      // 检查文件大小
      if (file.size / 1024 > this.fileSize) {
        this.$message.error(`文件大小不能超过 ${this.fileSize}KB`)
        return false
      }

      // 如果是图片且启用裁剪
      if (this.enableCrop && this.isImageFile(file)) {
        this.showCropDialog(file)
        return false // 阻止自动上传，等待裁剪完成
      }

      return true
    },

    // 检查文件类型
    checkFileType(file) {
      const ext = file.name.split('.').pop()?.toLowerCase()

      switch (this.uploadType) {
        case 'image':
          return this.imageTypes.includes(ext)
        case 'video':
          return this.videoTypes.includes(ext)
        case 'document':
          return this.documentTypes.includes(ext)
        default:
          return [...this.imageTypes, ...this.videoTypes, ...this.documentTypes].includes(ext)
      }
    },

    // 判断是否为图片文件
    isImageFile(file) {
      const ext = file.name.split('.').pop()?.toLowerCase()
      return this.imageTypes.includes(ext)
    },

    // 显示裁剪对话框
    showCropDialog(file) {
      this.currentCropFile = file
      this.cropOutputType = file.type.includes('png') ? 'png' : 'jpeg'

      const reader = new FileReader()
      reader.onload = (e) => {
        this.cropImageSrc = e.target.result
        this.cropDialogVisible = true
      }
      reader.readAsDataURL(file)
    },

    // 关闭裁剪对话框
    closeCropDialog() {
      this.cropDialogVisible = false
      this.cropImageSrc = ''
      this.currentCropFile = null
    },

    // 确认裁剪
    confirmCrop() {
      this.$refs.cropper.getCropBlob((blob) => {
        // 创建新的文件对象
        const croppedFile = new File([blob], this.currentCropFile.name, {
          type: blob.type,
          lastModified: Date.now()
        })

        // 上传裁剪后的文件
        this.uploadFile(croppedFile)
        this.closeCropDialog()
      })
    },

    // 手动上传文件
    uploadFile(file) {
      const formData = new FormData()
      formData.append('file', file)

      axios.post(this.uploadUrl, formData, {
        headers: this.uploadHeaders
      }).then(response => {
        this.handleSuccess(response.data, file)
      }).catch(error => {
        this.handleError(error, file)
      })
    },

    // 上传成功处理
    handleSuccess(response, file) {
      if (response.data && response.data.file) {
        const newFile = {
          name: file.name || response.data.file.name,
          url: response.data.file.url,
          type: this.getFileType(response.data.file.url)
        }

        if (this.multiple) {
          this.fileList.push(newFile)
        } else {
          this.fileList = [newFile]
        }

        this.updateValue()
        this.$message.success('上传成功')
      } else {
        this.$message.error('上传失败')
      }
    },

    // 上传失败处理
    handleError(error, file) {
      console.error('Upload error:', error)
      this.$message.error('上传失败')
    },

    // 移除文件
    removeFile(index) {
      this.fileList.splice(index, 1)
      this.updateValue()
    },

    // 处理文档上传移除
    handleRemove(file, fileList) {
      this.documentFileList = fileList
      this.fileList = fileList.map(f => ({
        name: f.name,
        url: f.url,
        type: this.getFileType(f.url)
      }))
      this.updateValue()
    },

    // 更新值
    updateValue() {
      if (this.multiple) {
        this.$emit('change', this.fileList)
      } else {
        this.$emit('change', this.fileList[0] || null)
      }
    },

    // 预览文件
    previewFileHandler(file) {
      this.previewFile = file
      this.previewDialogVisible = true
    },

    // 下载文件
    downloadFile(file) {
      const link = document.createElement('a')
      link.href = file.url
      link.download = file.name
      link.click()
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-upload-wrapper {
  .upload-container {
    border: 2px dashed #d9d9d9;
    border-radius: 5px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: border-color 0.3s;
    
    &:hover {
      border-color: #409eff;
    }
    
    .image-video-uploader {
      width: 100%;
      height: 100%;
      
      ::v-deep .el-upload {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    
    .upload-content {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    
    .file-preview-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      width: 100%;
      height: 100%;
      padding: 8px;
    }
    
    .file-preview-item {
      position: relative;
      width: calc(50% - 4px);
      height: calc(50% - 4px);
      border-radius: 4px;
      overflow: hidden;
      
      &:hover .file-actions {
        opacity: 1;
      }
      
      .preview-image,
      .preview-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .file-actions {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        opacity: 0;
        transition: opacity 0.3s;
        
        i {
          color: white;
          font-size: 16px;
          cursor: pointer;
          
          &:hover {
            color: #409eff;
          }
        }
      }
    }
    
    .upload-trigger {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #8c939d;
      
      .upload-icon {
        font-size: 28px;
        margin-bottom: 8px;
      }
      
      .upload-text {
        font-size: 14px;
      }
    }
  }
  
  .document-upload {
    .document-uploader {
      ::v-deep .el-upload__tip {
        margin-top: 8px;
        color: #606266;
        font-size: 12px;
      }
    }
  }
  
  .crop-container {
    height: 400px;
    width: 100%;
  }
  
  .preview-container {
    text-align: center;
    
    .preview-full-image {
      max-width: 100%;
      max-height: 70vh;
    }
    
    .preview-full-video {
      max-width: 100%;
      max-height: 70vh;
    }
    
    .preview-pdf {
      width: 100%;
      height: 70vh;
    }
    
    .preview-document {
      padding: 40px;
      
      i {
        font-size: 64px;
        color: #909399;
        margin-bottom: 16px;
      }
      
      p {
        margin: 16px 0;
        font-size: 16px;
        color: #606266;
      }
    }
  }
}
</style>
