<template>
  <div class="custom-upload-example">
    <h2>自定义上传组件使用示例</h2>
    
    <!-- 图片上传示例 -->
    <div class="example-section">
      <h3>1. 图片上传（单选）- 图片大小与容器一致</h3>
      <div class="upload-demo-row">
        <div class="upload-demo-item">
          <h4>正方形容器 (200x200)</h4>
          <custom-upload
            v-model="imageFile"
            upload-type="image"
            :width="200"
            :height="200"
            :enable-crop="true"
            :crop-width="150"
            :crop-height="150"
            :crop-fixed="true"
            :crop-fixed-number="[1, 1]"
          />
        </div>
        <div class="upload-demo-item">
          <h4>长方形容器 (300x200)</h4>
          <custom-upload
            v-model="imageFile2"
            upload-type="image"
            :width="300"
            :height="200"
            :enable-crop="true"
            :crop-width="300"
            :crop-height="200"
            :crop-fixed="true"
            :crop-fixed-number="[3, 2]"
          />
        </div>
        <div class="upload-demo-item">
          <h4>小尺寸容器 (120x120)</h4>
          <custom-upload
            v-model="imageFile3"
            upload-type="image"
            :width="120"
            :height="120"
          />
        </div>
      </div>
      <p>当前值: {{ imageFile }}</p>
    </div>

    <!-- 图片上传多选示例 -->
    <div class="example-section">
      <h3>2. 图片上传（多选）</h3>
      <custom-upload
        v-model="imageFiles"
        upload-type="image"
        :multiple="true"
        :width="300"
        :height="200"
      />
      <p>当前值: {{ imageFiles }}</p>
    </div>

    <!-- 视频上传示例 -->
    <div class="example-section">
      <h3>3. 视频上传</h3>
      <custom-upload
        v-model="videoFile"
        upload-type="video"
        :width="250"
        :height="180"
        :file-size="10240"
      />
      <p>当前值: {{ videoFile }}</p>
    </div>

    <!-- 文档上传示例 -->
    <div class="example-section">
      <h3>4. 文档上传</h3>
      <custom-upload
        v-model="documentFiles"
        upload-type="document"
        :multiple="true"
      />
      <p>当前值: {{ documentFiles }}</p>
    </div>

    <!-- 自动识别类型上传示例 -->
    <div class="example-section">
      <h3>5. 自动识别文件类型</h3>
      <custom-upload
        v-model="autoFiles"
        upload-type="auto"
        :multiple="true"
        :width="280"
        :height="200"
      />
      <p>当前值: {{ autoFiles }}</p>
    </div>

    <!-- 自定义尺寸和裁剪比例 -->
    <div class="example-section">
      <h3>6. 自定义裁剪比例（16:9）</h3>
      <custom-upload
        v-model="customCropFile"
        upload-type="image"
        :width="320"
        :height="180"
        :enable-crop="true"
        :crop-width="320"
        :crop-height="180"
        :crop-fixed="true"
        :crop-fixed-number="[16, 9]"
      />
      <p>当前值: {{ customCropFile }}</p>
    </div>

    <!-- 禁用状态示例 -->
    <div class="example-section">
      <h3>7. 禁用状态</h3>
      <custom-upload
        v-model="disabledFile"
        upload-type="image"
        :disabled="true"
        :width="200"
        :height="200"
      />
      <el-button @click="toggleDisabled">{{ disabled ? '启用' : '禁用' }}</el-button>
    </div>

    <!-- 自定义文件类型 -->
    <div class="example-section">
      <h3>8. 自定义接受文件类型（仅PNG和JPG）</h3>
      <custom-upload
        v-model="customTypeFile"
        upload-type="image"
        accept=".png,.jpg,.jpeg"
        :width="200"
        :height="200"
      />
      <p>当前值: {{ customTypeFile }}</p>
    </div>

    <!-- 表单集成示例 -->
    <div class="example-section">
      <h3>9. 表单集成示例</h3>
      <el-form :model="form" :rules="rules" ref="form" label-width="120px">
        <el-form-item label="产品名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入产品名称"></el-input>
        </el-form-item>
        
        <el-form-item label="产品图片" prop="image">
          <custom-upload
            v-model="form.image"
            upload-type="image"
            :enable-crop="true"
            :width="200"
            :height="200"
          />
        </el-form-item>
        
        <el-form-item label="产品视频" prop="video">
          <custom-upload
            v-model="form.video"
            upload-type="video"
            :width="300"
            :height="200"
          />
        </el-form-item>
        
        <el-form-item label="产品文档" prop="documents">
          <custom-upload
            v-model="form.documents"
            upload-type="document"
            :multiple="true"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import CustomUpload from './CustomUpload.vue'

export default {
  name: 'CustomUploadExample',
  components: {
    CustomUpload
  },
  data() {
    return {
      // 各种上传示例的数据
      imageFile: null,
      imageFile2: null,
      imageFile3: null,
      imageFiles: [],
      videoFile: null,
      documentFiles: [],
      autoFiles: [],
      customCropFile: null,
      disabledFile: null,
      customTypeFile: null,
      disabled: false,
      
      // 表单示例数据
      form: {
        name: '',
        image: null,
        video: null,
        documents: []
      },
      
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '请输入产品名称', trigger: 'blur' }
        ],
        image: [
          { required: true, message: '请上传产品图片', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    // 切换禁用状态
    toggleDisabled() {
      this.disabled = !this.disabled
    },
    
    // 提交表单
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log('表单数据:', this.form)
          this.$message.success('提交成功！')
        } else {
          this.$message.error('请完善表单信息')
          return false
        }
      })
    },
    
    // 重置表单
    resetForm() {
      this.$refs.form.resetFields()
      this.form = {
        name: '',
        image: null,
        video: null,
        documents: []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-upload-example {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  h2 {
    color: #303133;
    margin-bottom: 30px;
    text-align: center;
  }
  
  .example-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    background-color: #fafafa;

    h3 {
      color: #606266;
      margin-bottom: 15px;
      font-size: 16px;
    }

    h4 {
      color: #909399;
      margin-bottom: 10px;
      font-size: 14px;
      text-align: center;
    }

    p {
      margin-top: 15px;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
      font-size: 12px;
      color: #909399;
      word-break: break-all;
    }

    .upload-demo-row {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
      justify-content: flex-start;

      .upload-demo-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        h4 {
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
