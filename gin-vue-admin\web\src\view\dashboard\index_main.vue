<template>
    <div class="custom-upload-test">
      <div class="container">
        <h1>自定义上传组件测试页面</h1>
        
        <!-- 基础图片上传测试 -->
        <el-card class="test-card">
          <div slot="header">
            <span>基础图片上传</span>
          </div>
          <custom-upload
            v-model="basicImage"
            upload-type="image"
            :width="200"
            :height="200"
          />
          <div class="result">
            <strong>结果:</strong> {{ basicImage }}
          </div>
        </el-card>
  
        <!-- 图片裁剪测试 -->
        <el-card class="test-card">
          <div slot="header">
            <span>图片裁剪功能</span>
          </div>
          <custom-upload
            v-model="croppedImage"
            upload-type="image"
            :width="200"
            :height="200"
            :enable-crop="true"
            :crop-width="150"
            :crop-height="150"
            :crop-fixed="true"
            :crop-fixed-number="[1, 1]"
          />
          <div class="result">
            <strong>结果:</strong> {{ croppedImage }}
          </div>
        </el-card>
  
        <!-- 多选图片测试 -->
        <el-card class="test-card">
          <div slot="header">
            <span>多选图片上传</span>
          </div>
          <custom-upload
            v-model="multipleImages"
            upload-type="image"
            :multiple="true"
            :width="300"
            :height="200"
          />
          <div class="result">
            <strong>结果:</strong> {{ multipleImages }}
          </div>
        </el-card>
  
        <!-- 视频上传测试 -->
        <el-card class="test-card">
          <div slot="header">
            <span>视频上传</span>
          </div>
          <custom-upload
            v-model="videoFile"
            upload-type="video"
            :width="300"
            :height="200"
            :file-size="10240"
          />
          <div class="result">
            <strong>结果:</strong> {{ videoFile }}
          </div>
        </el-card>
  
        <!-- 文档上传测试 -->
        <el-card class="test-card">
          <div slot="header">
            <span>文档上传</span>
          </div>
          <custom-upload
            v-model="documents"
            upload-type="document"
            :multiple="true"
          />
          <div class="result">
            <strong>结果:</strong> {{ documents }}
          </div>
        </el-card>
  
        <!-- 自动识别类型测试 -->
        <el-card class="test-card">
          <div slot="header">
            <span>自动识别文件类型</span>
          </div>
          <custom-upload
            v-model="autoFiles"
            upload-type="auto"
            :multiple="true"
            :width="280"
            :height="200"
          />
          <div class="result">
            <strong>结果:</strong> {{ autoFiles }}
          </div>
        </el-card>
  
        <!-- 自定义尺寸和比例测试 -->
        <el-card class="test-card">
          <div slot="header">
            <span>自定义裁剪比例 (16:9)</span>
          </div>
          <custom-upload
            v-model="customRatioImage"
            upload-type="image"
            :width="320"
            :height="180"
            :enable-crop="true"
            :crop-width="320"
            :crop-height="180"
            :crop-fixed="true"
            :crop-fixed-number="[16, 9]"
          />
          <div class="result">
            <strong>结果:</strong> {{ customRatioImage }}
          </div>
        </el-card>
  
        <!-- 表单集成测试 -->
        <el-card class="test-card">
          <div slot="header">
            <span>表单集成测试</span>
          </div>
          <el-form :model="testForm" :rules="formRules" ref="testForm" label-width="120px">
            <el-form-item label="产品名称" prop="name">
              <el-input v-model="testForm.name" placeholder="请输入产品名称"></el-input>
            </el-form-item>
            
            <el-form-item label="产品图片" prop="image">
              <custom-upload
                v-model="testForm.image"
                upload-type="image"
                :enable-crop="true"
                :width="200"
                :height="200"
              />
            </el-form-item>
            
            <el-form-item label="产品视频" prop="video">
              <custom-upload
                v-model="testForm.video"
                upload-type="video"
                :width="300"
                :height="200"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="submitForm">提交测试</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
  
        <!-- 测试结果显示 -->
        <el-card class="test-card">
          <div slot="header">
            <span>所有测试数据</span>
          </div>
          <pre class="test-data">{{ allTestData }}</pre>
        </el-card>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'CustomUploadTest',
    data() {
      return {
        // 各种测试数据
        basicImage: null,
        croppedImage: null,
        multipleImages: [],
        videoFile: null,
        documents: [],
        autoFiles: [],
        customRatioImage: null,
        
        // 表单测试数据
        testForm: {
          name: '',
          image: null,
          video: null
        },
        
        // 表单验证规则
        formRules: {
          name: [
            { required: true, message: '请输入产品名称', trigger: 'blur' }
          ],
          image: [
            { required: true, message: '请上传产品图片', trigger: 'change' }
          ]
        }
      }
    },
    computed: {
      // 所有测试数据
      allTestData() {
        return {
          basicImage: this.basicImage,
          croppedImage: this.croppedImage,
          multipleImages: this.multipleImages,
          videoFile: this.videoFile,
          documents: this.documents,
          autoFiles: this.autoFiles,
          customRatioImage: this.customRatioImage,
          testForm: this.testForm
        }
      }
    },
    methods: {
      // 提交表单测试
      submitForm() {
        this.$refs.testForm.validate((valid) => {
          if (valid) {
            console.log('表单数据:', this.testForm)
            this.$message.success('表单提交成功！')
            this.$message({
              message: `表单数据: ${JSON.stringify(this.testForm, null, 2)}`,
              type: 'success',
              duration: 5000
            })
          } else {
            this.$message.error('请完善表单信息')
            return false
          }
        })
      },
      
      // 重置表单
      resetForm() {
        this.$refs.testForm.resetFields()
        this.testForm = {
          name: '',
          image: null,
          video: null
        }
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .custom-upload-test {
    min-height: 100vh;
    background-color: #f5f5f5;
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      
      h1 {
        text-align: center;
        color: #303133;
        margin-bottom: 30px;
        font-size: 28px;
      }
      
      .test-card {
        margin-bottom: 30px;
        
        .result {
          margin-top: 15px;
          padding: 10px;
          background-color: #f5f7fa;
          border-radius: 4px;
          font-size: 12px;
          color: #606266;
          word-break: break-all;
          
          strong {
            color: #303133;
          }
        }
        
        .test-data {
          background-color: #f5f7fa;
          padding: 15px;
          border-radius: 4px;
          font-size: 12px;
          color: #606266;
          max-height: 400px;
          overflow-y: auto;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
    }
  }
  </style>
  