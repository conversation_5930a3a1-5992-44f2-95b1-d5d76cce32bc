{"name": "gin-vue-admin", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@antv/dom-util": "2.0.2", "@antv/g-canvas": "^0.4.12", "@antv/g6": "3.5.2", "@antv/matrix-util": "2.0.7", "@antv/util": "~2.0.9", "@moefe/vue-aplayer": "^2.0.0-beta.5", "address-smart-parse": "^1.0.0", "axios": "^0.21.1", "cnchar": "^3.2.2", "core-js": "^3.6.5", "echarts": "^5.5.0", "egrid": "^1.1.2", "element-china-area-data": "^5.0.2", "element-ui": "^2.15.1", "html2canvas": "^1.4.1", "js-base64": "^3.7.3", "jspdf": "^2.5.1", "ml-matrix": "6.10.0", "path": "^0.12.7", "pinyin-match": "^1.2.4", "qrcodejs2": "0.0.2", "qs": "^6.8.0", "quill": "^1.3.7", "quill-html-edit-button": "^2.x", "quill-image-drop-module": "^1.0.3", "quill-image-extend-module": "^1.1.2", "quill-image-resize-module": "^3.0.0", "screenfull": "^5.0.2", "script-ext-html-webpack-plugin": "^2.1.4", "spark-md5": "^3.0.1", "timeline-vuejs": "1.1.1", "video.js": "^8.3.0", "videojs-contrib-hls": "^5.15.0", "vue": "2.6.10", "vue-class-component": "^7.1.0", "vue-cropper": "^0.6.5", "vue-json-pretty": "^1.6.0", "vue-particle-line": "^0.1.4", "vue-quill-editor": "^3.0.6", "vue-router": "^3.1.3", "vue-simple-uploader": "^0.7.4", "vuedraggable": "^2.24.3", "vuescroll": "^4.14.4", "vuex": "^3.1.1", "vuex-persist": "^2.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.6", "@vue/cli-plugin-eslint": "^4.5.6", "@vue/cli-service": "^4.5.6", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "numericjs": "^1.2.6", "pug": "^2.0.4", "pug-plain-loader": "^1.0.0", "raw-loader": "^3.1.0", "sass": "^1.69.5", "sass-loader": "^8.0.2", "vue-template-compiler": "2.6.10", "webpack-dev-server": "^3.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"]}