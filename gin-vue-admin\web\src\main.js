import Vue from 'vue'
import App from './App.vue'
// 引入element
import ElementUI from 'element-ui';
// import 'element-ui/lib/theme-chalk/index.css';
import '@/style/theme/index.css';
// 全局配置elementui的dialog不能通过点击遮罩层关闭
ElementUI.Dialog.props.closeOnClickModal.default = false;
ElementUI.Dialog.props.closeOnPressEscape.default = false;
//medium
Vue.use(ElementUI, {size: 'medium'});
// 引入封装的router
import router from '@/router/index'
import Pagination from "@/components/Pagination/index";

// time line css
import '../node_modules/timeline-vuejs/dist/timeline-vuejs.css'

import '@/permission'
import {store} from '@/store/index'
import MCard from "@/components/mCard/index"
import MImage from "@/components/mImage/index"
import fn from "@/utils/fun"
import ls from "@/utils/ls"
import {formatTimeToStr} from "@/utils/date";
import MSteps from "@/components/mComponents/mSteps"
import ImgsHouseDialog from "@/components/mComponents/imgsHouseDialog"
import "@/assets/font_icon/iconfont.css";
import MEditor from "@/components/mComponents/mEditor"
import MNumInput from "@/components/mNumInput/index"
import Print from "@/utils/print";

Vue.use(Print)
//  格式化日期
Vue.filter('formatDate', function (time) {
    if (time != null && time != "") {
        var date = new Date(time);
        return formatTimeToStr(date, "yyyy-MM-dd hh:mm:ss");
    } else {
        return "";
    }
})
// 格式化分转元
Vue.filter('formatF2Y', function (number) {
    if (!number) {
        return fn.changeMoneyF2Y(0) == "" ? "0.00" : fn.changeMoneyF2Y(0);
    }
    return fn.changeMoneyF2Y(number);
    // if (number != null && number != "") {
    //     return fn.changeMoneyF2Y(number)
    // } else {
    //     return "";
    // }
})
// 格式化状态(上架下架)
Vue.filter('formatState', function (status) {
    let formatState = "";
    switch (status) {
        case 0:
            formatState = "下架";
            break;
        case 1:
            formatState = "上架";
            break;
        default:
            break;
    }

    return formatState;

    // if (status != null && status != "") {
    //     return status === 0 ? "下架" : "上架"
    // } else {
    //     console.log("formatState");
    //     return "";
    // }
})

Vue.filter('formatPayType', function (status) {

    let map = {};
    map[1] = "汇聚余额";
    map[2] = "站内余额";
    map[5] = "汇聚微信支付";

    return map[status];
})
Vue.config.productionTip = false

// 路由守卫
import Bus from '@/utils/bus.js'

Vue.use(Bus)

import APlayer from '@moefe/vue-aplayer';

Vue.use(APlayer, {
    defaultCover: 'https://github.com/u3u.png',
    productionTip: true,
});


import {auth} from '@/directive/auth'
// 按钮权限指令
auth(Vue)

import uploader from 'vue-simple-uploader'
import CustomUpload from '@/components/CustomUpload.vue'

Vue.use(uploader)
Vue.component('CustomUpload', CustomUpload)

export default new Vue({
    render: h => h(App),
    router,
    store
}).$mount('#app')

//引入echarts
import echarts from 'echarts'

Vue.prototype.$echarts = echarts;
Vue.prototype.$fn = fn;
Vue.prototype.$ls = ls;
// 分 转换
Vue.prototype.$changeMoney = fn.changeMoney;
Vue.prototype.$changeMoneyF2Y = fn.changeMoneyF2Y;
Vue.prototype.$changeMoneyY2F = fn.changeMoneyY2F;
Vue.prototype.$path = process.env.VUE_APP_BASE_API;
Vue.prototype.$_blank = (link, query = {}) => {
    let routeUrl = router.resolve({
        path: link,
        query
    })
    window.open(routeUrl.href)
};
Vue.component('MCard', MCard);
Vue.component('MImage', MImage);
Vue.component('Pagination', Pagination);
Vue.component('MSteps', MSteps);
Vue.component('ImgsHouseDialog', ImgsHouseDialog);
Vue.component('MEditor', MEditor);
Vue.component('MNumInput', MNumInput);

/* console.log(`
       欢迎使用 Gin-Vue-Admin
       当前版本:V2.3.9
       加群方式:微信：shouzi_1994 QQ群：622360840
       默认自动化文档地址:http://127.0.0.1%s/swagger/index.html
       默认前端文件运行地址:http://127.0.0.1:8080
       如果项目让您获得了收益，希望您能请团队喝杯可乐:https://www.gin-vue-admin.com/docs/coffee
`) */
