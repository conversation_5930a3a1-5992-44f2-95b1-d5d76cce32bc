# CustomUpload 自定义上传组件

一个功能强大的 Vue 2 文件上传组件，支持图片、视频、文档上传，具备图片裁剪、文件预览等功能。

## 功能特性

### 🎯 核心功能
- ✅ 保留所有 el-upload 组件的方法、属性和事件
- ✅ 自动文件类型检测（图像、视频、文档）
- ✅ 支持单选和多选文件上传
- ✅ 可配置的容器尺寸

### 🎨 视觉设计
- ✅ 5像素圆角虚线边框设计
- ✅ 居中显示加号图标（+）
- ✅ 响应式布局和悬停效果
- ✅ 文件预览缩略图

### ✂️ 图片处理
- ✅ 上传前强制手动裁剪功能
- ✅ 可配置裁剪区域尺寸
- ✅ 支持固定裁剪比例
- ✅ 集成 vue-cropper 裁剪库

### 👁️ 预览功能
- ✅ 图像：缩略图/灯箱预览
- ✅ 视频：内置视频播放器
- ✅ PDF：嵌入式 PDF 查看器
- ✅ 文档：下载功能

## 安装依赖

```bash
npm install vue-cropper --save
```

## 基础用法

### 1. 图片上传（单选）

```vue
<template>
  <custom-upload
    v-model="imageFile"
    upload-type="image"
    :width="200"
    :height="200"
  />
</template>

<script>
import CustomUpload from '@/components/CustomUpload.vue'

export default {
  components: {
    CustomUpload
  },
  data() {
    return {
      imageFile: null
    }
  }
}
</script>
```

### 2. 图片上传（多选 + 裁剪 + 限制数量）

```vue
<template>
  <custom-upload
    v-model="imageFiles"
    upload-type="image"
    :multiple="true"
    :max-count="6"
    :enable-crop="true"
    :crop-width="300"
    :crop-height="200"
    :crop-fixed="true"
    :crop-fixed-number="[3, 2]"
  />
</template>
```

### 3. 视频上传

```vue
<template>
  <custom-upload
    v-model="videoFile"
    upload-type="video"
    :width="300"
    :height="200"
    :file-size="10240"
  />
</template>
```

### 4. 文档上传

```vue
<template>
  <custom-upload
    v-model="documentFiles"
    upload-type="document"
    :multiple="true"
  />
</template>
```

### 5. 自动识别文件类型

```vue
<template>
  <custom-upload
    v-model="files"
    upload-type="auto"
    :multiple="true"
  />
</template>
```

## API 文档

### Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| value / v-model | 绑定值 | String / Array | — | [] |
| upload-type | 上传类型 | String | image / video / document / auto | auto |
| multiple | 是否支持多选 | Boolean | — | false |
| width | 容器宽度 | String / Number | — | 180 |
| height | 容器高度 | String / Number | — | 180 |
| enable-crop | 是否启用裁剪 | Boolean | — | false |
| crop-width | 裁剪宽度 | Number | — | 200 |
| crop-height | 裁剪高度 | Number | — | 200 |
| crop-fixed | 是否固定裁剪比例 | Boolean | — | false |
| crop-fixed-number | 裁剪比例 [宽, 高] | Array | — | [1, 1] |
| file-size | 文件大小限制 (KB) | Number | — | 2048 |
| disabled | 是否禁用 | Boolean | — | false |
| accept | 自定义接受的文件类型 | String | — | '' |
| max-count | 最大上传数量 | Number | — | 9 |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 文件列表改变时触发 | (fileList) |

### 文件对象结构

```javascript
{
  name: 'filename.jpg',    // 文件名
  url: 'http://...',       // 文件URL
  type: 'image'            // 文件类型：image/video/document
}
```

## 支持的文件类型

### 图像文件
- jpg, jpeg, png, gif, webp, bmp, svg

### 视频文件
- mp4, avi, mov, wmv, flv, webm, mkv

### 文档文件
- pdf, doc, docx, xls, xlsx, ppt, pptx, txt

## 高级用法

### 表单集成

```vue
<template>
  <el-form :model="form" :rules="rules" ref="form">
    <el-form-item label="产品图片" prop="image">
      <custom-upload
        v-model="form.image"
        upload-type="image"
        :enable-crop="true"
      />
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  data() {
    return {
      form: {
        image: null
      },
      rules: {
        image: [
          { required: true, message: '请上传产品图片', trigger: 'change' }
        ]
      }
    }
  }
}
</script>
```

### 自定义样式

```vue
<template>
  <custom-upload
    v-model="files"
    :width="'100%'"
    :height="300"
    class="custom-style"
  />
</template>

<style>
.custom-style {
  border: 2px solid #409eff;
  border-radius: 10px;
}
</style>
```

## 注意事项

1. **依赖要求**：需要安装 `vue-cropper` 库
2. **Vue 版本**：适用于 Vue 2.x
3. **Element UI**：依赖 Element UI 组件库
4. **文件大小**：默认限制 2MB，可通过 `file-size` 属性调整
5. **裁剪功能**：仅对图片文件有效
6. **预览功能**：PDF 预览需要浏览器支持

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 更新日志

### v1.0.0
- 初始版本发布
- 支持图片、视频、文档上传
- 集成图片裁剪功能
- 支持文件预览
- 完整的 TypeScript 类型定义

## 许可证

MIT License
